<!DOCTYPE html>
<html>

<head>
	<meta charset="UTF-8">
	<title>ANNO Master</title>
	<script src="ext/jquery-3.6.0.min.js"></script>
	<script src="ext/toastify-js"></script>
	<link rel="stylesheet" href="ext/toastify.min.css" />
	<link rel="stylesheet" type="text/css" href="style.css">
	<script src="lib/util.js"></script>
	<script src="lib/request.js"></script>
	<script src="com.js"></script>
	<script src="login.js"></script>

	<script>
	$(document).ready(function () {

		const roleFld = {
			name: 'role',
			action: function(item) {
				showUpdateBox(['radio', 'kam', 'pm', 'admin', 'owner', 'manager', 'member', 'root',"superadmin", "demo", "test"], function(val) {
					if (val == 'owner' || val == 'manager') {
						if (item['org_uid'] == '') {
							throw 'user is not in an organization';
						}
						return {
							method: 'PUT',
							url: urlToTeam(item['org_uid'])+'/role',
							body: {role: val, user_uids: [item['uid']]},
						};
					}
					return {
						method: 'PATCH',
						url: urlToUser(item['uid']),
						params: {fields: 'role'},
						body: {role: val},
						onok: document.refreshTable,
					};
				});
			},
		};
		generateDataView({
			title: 'Users',
			fields: ['uid', 'name', 'phone', roleFld, orgUidFld(), 'tags', datetimeFld()],
			filter: {
				'uid': ['text'],
				'name_pattern': ['text'],
				'phones': ['text'],
				'org_uid': ['text'],
				'tags': ['text'],
				'roles': ['text'],
			},
			actions: {
				// foo_del: function (item) {
				// 	// Your delete implementation
				// 	console.log('Deleting row:', JSON.stringify(item));
				// },
			},
			list: {
				url: function() { return getBaseURL() + '/iam/v1/users' },
				// query: { 'param1': 'value1', 'param2': 'value2' },
				dataField: 'users',
			},
		});

		generateDataView({
			title: 'Teams',
			fields: ['uid', 'name', 'type', datetimeFld()],
			filter: {
				'uid': ['text'],
				'name_pattern': ['text'],
				'type': ['text'],
			},
			// actions: {
			// 	foo_del: function (item) {
			// 		// Your delete implementation
			// 		console.log('Deleting row:', JSON.stringify(item));
			// 	},
			// },
			list: {
				url: function() { return getBaseURL() + '/iam/v1/teams' },
				// query: { 'param1': 'value1', 'param2': 'value2' },
				dataField: 'teams',
			},
			links: {
				members: function(item) { return getBaseURL().replace('anno', 'admin') + '/admin/teams/members/'+item.uid }
			},
		});

		generateDataView({
			title: 'Files',
			fields: ['uid', 'name', 'state', 'uri', 'size', orgUidFld(), userUidFld('creator_uid'), datetimeFld()],
			filter: {
				'uid': ['text'],
				'name_pattern': ['text'],
				'uris': ['text'],
				'creator_uid': ['text'],
				'org_uid': ['text'],
			},
			actions: {
				download: async function (item) {
					try {
						let data = await httpSend('PUT', urlToFile(item['uid']) + '/share', {timeout: 3600*24});
						window.open(data.url, '_self', 'popup');
					} catch(e) {
						toastError(e);
					}
				},
			},
			list: {
				url: function() { return getBaseURL() + '/annofeed/v1/files' },
				// query: { 'param1': 'value1', 'param2': 'value2' },
				dataField: 'files',
			},
		});

		generateDataView({
			title: 'Datas',
			fields: ['uid', 'name', 'type', 'state', 'size', orgUidFld(), userUidFld('creator_uid'), orderUidFld(), datetimeFld()],
			filter: {
				'uid': ['text'],
				'name_pattern': ['text'],
				'states': ['text'],
				'creator_uid': ['text'],
				'org_uid': ['text'],
			},
			actions: {
				delete: async function (item) {
					try {
						await httpSend('DELETE', urlToData(item['uid']));
						document.refreshTable();
						toastInfo('success');
					} catch(e) {
						toastError(e);
					}
				},
			},
			list: {
				url: function() { return getBaseURL() + '/annofeed/v1/datas' },
				// query: { 'param1': 'value1', 'param2': 'value2' },
				dataField: 'datas',
			},
		});

		let lotAnnoFld = {
			name: 'anno_result_url',
			getValue: function(item) {
				let div = document.createElement('div');
				const action = async function (e) {
					try {
						await httpSend('PUT', urlToLot(item['uid']) + '/export-annos', {"option":1});
						document.refreshTable();
						toastInfo('success');
					} catch(e) {
						toastError(e);
					}
				};
				const action2 = async function (e) {
					try {
						await httpSend('PUT', urlToLot(item['uid']) + '/export-annos', {"option":0,"phases":[3,4]});
						document.refreshTable();
						toastInfo('success');
					} catch(e) {
						toastError(e);
					}
				};
				div.appendChild(newButton('导出已完成', action));
				div.appendChild(newButton('导出3,4阶段结果(大卓3D车道线)', action2));

				if (item['anno_result_url'] != '') {
					div.appendChild(newANode(item['anno_result_url'], 'download'));
				}
				return div;
			},
		};
		generateDataView({
			title: '任务(Lots)',
			fields: ['uid', 'name', 'type', 'data_type', 'state', 'job_ready', 'job_count', dataUidFld(), orderUidFld(),
					 orgUidFld(), userUidFld('creator_uid'), datetimeFld(), lotAnnoFld],
			filter: {
				'uid': ['text'],
				'name_pattern': ['text'],
				'org_uid': ['text'],
				'order_uid': ['text'],
				'states': ['text'],
				'type': ['text'],
				'creator_uid': ['text'],
			},
			actions: {
				// foo_del: function (item) {
				// 	// Your delete implementation
				// 	console.log('Deleting row:', JSON.stringify(item));
				// },
			},
			list: {
				url: function() { return getBaseURL() + '/anno/v1/lots' },
				// query: { 'param1': 'value1', 'param2': 'value2' },
				dataField: 'lots',
			},
			links: {
				Config: function(item) { return getBaseURL().replace('anno', 'admin') + '/admin/tasks/detail/'+item.uid },
				State: function(item) { return getBaseURL().replace('anno', 'admin') + '/admin/tasks/progress/'+item.uid },
				PickLabeler: function(item) { return getBaseURL().replace('anno', 'admin') + '/admin/tasks/assign/'+item.uid },
				ClaimJob: function(item) { return getBaseURL().replace('anno', 'label') + '/annotate/frame?lid='+item.uid },
			},
		});

		generateDataView({
			title: '任务包(Jobs)',
			fields: ['uid', 'idx_in_lot', 'phase', 'state', 'elems_cnt', 'ins_cnt','job_elem_clip', lotUidFld(), userUidFld('executor_uid'),
					userUidFld('last_executor'), orgUidFld('last_execteam'), datetimeFld('updated_at')],
			filter: {
				'filter.uids': ['text'],
				'filter.lot_uid': ['text'],
				'filter.jobclip': ['text'],
				'elem_name_pattern': ['text'],
			},
			actions: {
				查看操作日志: function (item) {
					showResource(getBaseURL() + '/anno/v1/jobs/' + item.uid + '/log');
				},
				打回: function (item) {
					revetVal(item);
				},
				用户分配: function (item) {
					assign(item);
				},
			},
			list: {
				url: function() { return getBaseURL() + '/anno/v1/jobs' },
				query: { 'show_last_executor': true },
				dataField: 'jobs',
			},
			links: {
			},
		});

		generateDataView({
			title: '订单(Orders)',
			fields: ['uid', 'name', 'type', 'state', 'size', orgUidFld(), userUidFld('creator_uid'), dataUidFld(), datetimeFld()],
			filter: {
				'uid': ['text'],
				'name_pattern': ['text'],
				'state': ['text'],
				'creator_uid': ['text'],
				'org_uid': ['text'],
			},
			actions: {
				cancel: async function (item) {
					try {
						await httpSend('PUT', urlToOrder(item['uid']) + '/cancel');
						document.refreshTable();
						toastInfo('success');
					} catch(e) {
						toastError(e);
					}
				},
				delete: async function (item) {
					try {
						await httpSend('DELETE', urlToOrder(item['uid']));
						document.refreshTable();
						toastInfo('success');
					} catch(e) {
						toastError(e);
					}
				},
			},
			list: {
				url: function() { return getBaseURL() + '/anno/v1/orders' },
				// query: { 'param1': 'value1', 'param2': 'value2' },
				dataField: 'orders',
			},
		});


	});

	</script>
</head>

<body>

</body>

</html>
