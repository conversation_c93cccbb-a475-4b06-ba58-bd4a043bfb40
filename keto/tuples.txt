# grep -v '^#' tuples.txt | $KETO relation-tuple parse - --format json | ketow relation-tuple create -

# service accounts
IamGroup:sys/service#members@IamUser:aaaaaaaanno
IamUser:aaaaaaaanno#policies@IamPolicy:sys/admin

# global groups
#IamGroup:sys/allUsers#members@IamUser:xxx
#IamGroup:sys/admin#members@IamUser:xxx
#IamGroup:sys/root#members@IamUser:xxx
#IamGroup:sys/inspector#members@IamUser:xxx
#IamGroup:sys/kam#members@IamUser:xxx
#IamGroup:sys/pm#members@IamUser:xxx

# global roles
# root, admin and service (the root and service are not actually used)
IamRole:root#perms@IamRole:admin#perms
IamRole:root#policies@IamPolicy:sys/admin
IamRole:service#perms@IamRole:admin#perms
IamRole:service#policies@IamPolicy:sys/admin
IamRole:superadmin#perms@IamRole:admin#perms
IamRole:superadmin#policies@IamPolicy:sys/admin

IamRole:inspector#perms@IamRole:viewer#perms
IamRole:inspector#policies@IamPolicy:sys/admin

IamRole:kam#perms@IamRole:iam.editor#perms
IamRole:kam#perms@IamRole:anno.kam#perms
#IamRole:kam#policies@IamPolicy:sys/admin

IamRole:pm#perms@IamRole:iam.viewer#perms
IamRole:pm#<EMAIL>
#IamRole:pm#perms@IamRole:anno.editor#perms
#IamRole:pm#policies@IamPolicy:sys/admin

IamRole:admin#perms@IamRole:iam.owner#perms
IamRole:admin#perms@IamRole:anno.owner#perms
IamRole:admin#perms@IamRole:anno.regulator#perms
IamRole:admin#perms@IamRole:annofeed.owner#perms
IamRole:admin#perms@IamRole:fe.owner#perms
IamRole:admin#policies@IamPolicy:sys/admin

IamRole:subAdmin#perms@IamRole:iam.owner#perms
IamRole:subAdmin#perms@IamRole:anno.owner#perms
IamRole:subAdmin#perms@IamRole:anno.regulator#perms
IamRole:subAdmin#perms@IamRole:annofeed.owner#perms
IamRole:subAdmin#perms@IamRole:fe.owner#perms
IamRole:subAdmin#policies@IamPolicy:sys/admin

IamRole:viewer#perms@IamRole:iam.viewer#perms
IamRole:viewer#perms@IamRole:anno.viewer#perms
IamRole:viewer#perms@IamRole:annofeed.viewer#perms
IamRole:viewer#perms@IamRole:fe.viewer#perms
IamRole:viewer#policies@IamPolicy:sys/admin

# global policies
#IamPolicy:sys/root#roles@IamRole:admin
#IamPolicy:sys/root#users@IamGroup:sys/root#members

IamPolicy:sys/admin#roles@IamRole:admin
IamPolicy:sys/admin#users@IamGroup:sys/admin#members
IamPolicy:sys/admin#users@IamGroup:sys/service#members
IamPolicy:sys/admin#users@IamGroup:sys/root#members
IamPolicy:sys/admin#users@IamGroup:sys/superadmin#members

IamPolicy:sys/inspector#roles@IamRole:inspector
IamPolicy:sys/inspector#users@IamGroup:sys/inspector#members

#IamPolicy:sys/subAdmin#roles@IamRole:subAdmin
#IamPolicy:sys/subAdmin#users@IamGroup:subAdmin#members

#IamPolicy:viewer#roles@IamRole:viewer
#IamPolicy:viewer#users@IamGroup:viewer#members

IamPolicy:sys/publicView#roles@IamRole:viewer
IamPolicy:sys/publicView#users@IamGroup:sys/allUsers#members

IamPolicy:sys/kam#roles@IamRole:kam
IamPolicy:sys/kam#users@IamGroup:sys/kam#members
IamPolicy:sys/pm#roles@IamRole:pm
IamPolicy:sys/pm#users@IamGroup:sys/pm#members

# demo policy
IamPolicy:sys/demo#roles@IamRole:demo
IamPolicy:sys/demo#users@IamGroup:sys/demo#members

# roles to be used in a team
IamRole:member#perms@IamRole:iam.viewer#perms
IamRole:member#perms@IamRole:anno.viewer#perms
IamRole:member#perms@IamRole:annofeed.viewer#perms
IamRole:member#policies@IamPolicy:sys/admin
IamRole:member#policies@IamPolicy:sys/publicView

IamRole:manager#perms@IamRole:iam.editor#perms
IamRole:manager#perms@IamRole:anno.editor#perms
IamRole:manager#perms@IamRole:annofeed.editor#perms
IamRole:manager#policies@IamPolicy:sys/admin
IamRole:manager#policies@IamPolicy:sys/publicView

IamRole:owner#perms@IamRole:iam.owner#perms
IamRole:owner#perms@IamRole:anno.owner#perms
IamRole:owner#perms@IamRole:annofeed.owner#perms
IamRole:owner#policies@IamPolicy:sys/admin
IamRole:owner#policies@IamPolicy:sys/publicView

# demo role with limited permissions
IamRole:demo#perms@IamRole:iam.viewer#perms
IamRole:demo#perms@IamRole:anno.owner#perms
IamRole:demo#perms@IamRole:annofeed.owner#perms
IamRole:demo#policies@IamPolicy:sys/admin
IamRole:demo#policies@IamPolicy:sys/publicView

# IAM roles and permissions
IamRole:IamRole.viewer#<EMAIL>
IamRole:IamRole.viewer#<EMAIL>
IamRole:IamRole.editor#perms@IamRole:IamRole.viewer#perms
IamRole:IamRole.editor#<EMAIL>
IamRole:IamRole.editor#<EMAIL>
IamRole:IamRole.owner#perms@IamRole:IamRole.editor#perms
IamRole:IamRole.owner#<EMAIL>
IamRole:IamRole.owner#<EMAIL>
IamRole:IamRole.owner#<EMAIL>
IamRole:IamRole.owner#policies@IamPolicy:sys/admin
IamRole:IamRole.owner#policies@IamPolicy:sys/publicView

IamRole:IamUser.viewer#<EMAIL>
IamRole:IamUser.viewer#<EMAIL>
IamRole:IamUser.editor#perms@IamRole:IamUser.viewer#perms
IamRole:IamUser.editor#<EMAIL>
IamRole:IamUser.editor#<EMAIL>
IamRole:IamUser.editor#<EMAIL>
IamRole:IamUser.editor#<EMAIL>
IamRole:IamUser.owner#perms@IamRole:IamUser.editor#perms
IamRole:IamUser.owner#<EMAIL>
IamRole:IamUser.owner#<EMAIL>
IamRole:IamUser.owner#<EMAIL>
IamRole:IamUser.owner#policies@IamPolicy:sys/admin
IamRole:IamUser.owner#policies@IamPolicy:sys/publicView

IamRole:IamGroup.viewer#<EMAIL>
IamRole:IamGroup.viewer#<EMAIL>
IamRole:IamGroup.viewer#<EMAIL>
IamRole:IamGroup.viewer#policies@IamPolicy:sys/admin
IamRole:IamGroup.viewer#policies@IamPolicy:sys/publicView
IamRole:IamGroup.editor#perms@IamRole:IamGroup.viewer#perms
IamRole:IamGroup.editor#<EMAIL>
IamRole:IamGroup.editor#<EMAIL>
IamRole:IamGroup.editor#<EMAIL>
IamRole:IamGroup.editor#<EMAIL>
IamRole:IamGroup.editor#<EMAIL>
IamRole:IamGroup.editor#<EMAIL>
IamRole:IamGroup.editor#<EMAIL>
IamRole:IamGroup.editor#policies@IamPolicy:sys/admin
IamRole:IamGroup.editor#policies@IamPolicy:sys/publicView
IamRole:IamGroup.owner#perms@IamRole:IamGroup.editor#perms
IamRole:IamGroup.owner#<EMAIL>
IamRole:IamGroup.owner#<EMAIL>
IamRole:IamGroup.owner#<EMAIL>
IamRole:IamGroup.owner#policies@IamPolicy:sys/admin
IamRole:IamGroup.owner#policies@IamPolicy:sys/publicView

IamRole:iam.viewer#perms@IamRole:IamRole.viewer#perms
IamRole:iam.viewer#perms@IamRole:IamUser.viewer#perms
IamRole:iam.viewer#perms@IamRole:IamGroup.viewer#perms
IamRole:iam.viewer#policies@IamPolicy:sys/admin
IamRole:iam.viewer#policies@IamPolicy:sys/publicView

IamRole:iam.editor#perms@IamRole:IamRole.editor#perms
IamRole:iam.editor#perms@IamRole:IamUser.editor#perms
IamRole:iam.editor#perms@IamRole:IamGroup.editor#perms
IamRole:iam.editor#policies@IamPolicy:sys/admin
IamRole:iam.editor#policies@IamPolicy:sys/publicView

IamRole:iam.owner#perms@IamRole:IamRole.owner#perms
IamRole:iam.owner#perms@IamRole:IamUser.owner#perms
IamRole:iam.owner#perms@IamRole:IamGroup.owner#perms
IamRole:iam.owner#policies@IamPolicy:sys/admin
IamRole:iam.owner#policies@IamPolicy:sys/publicView

# anno roles and permissions
IamRole:AnnoLot.viewer#<EMAIL>
IamRole:AnnoLot.viewer#<EMAIL>
IamRole:AnnoLot.viewer#policies@IamPolicy:sys/admin
IamRole:AnnoLot.viewer#policies@IamPolicy:sys/publicView
IamRole:AnnoLot.editor#perms@IamRole:AnnoLot.viewer#perms
IamRole:AnnoLot.editor#<EMAIL>
IamRole:AnnoLot.editor#<EMAIL>
#IamRole:AnnoLot.editor#<EMAIL>
#IamRole:AnnoLot.editor#<EMAIL>
#IamRole:AnnoLot.editor#<EMAIL>
IamRole:AnnoLot.editor#<EMAIL>
IamRole:AnnoLot.editor#<EMAIL>
IamRole:AnnoLot.editor#<EMAIL>
IamRole:AnnoLot.editor#policies@IamPolicy:sys/admin
IamRole:AnnoLot.editor#policies@IamPolicy:sys/publicView
IamRole:AnnoLot.owner#perms@IamRole:AnnoLot.editor#perms
IamRole:AnnoLot.owner#<EMAIL>
IamRole:AnnoLot.owner#<EMAIL>
IamRole:AnnoLot.owner#<EMAIL>
IamRole:AnnoLot.owner#policies@IamPolicy:sys/admin
IamRole:AnnoLot.owner#policies@IamPolicy:sys/publicView

IamRole:AnnoLot.executor#<EMAIL>
IamRole:AnnoLot.executor#policies@IamPolicy:sys/admin
IamRole:AnnoLot.executor#policies@IamPolicy:sys/publicView

IamRole:AnnoJob.viewer#<EMAIL>
IamRole:AnnoJob.viewer#<EMAIL>
IamRole:AnnoJob.viewer#<EMAIL>
IamRole:AnnoJob.viewer#<EMAIL>
IamRole:AnnoJob.viewer#policies@IamPolicy:sys/admin
IamRole:AnnoJob.viewer#policies@IamPolicy:sys/publicView
IamRole:AnnoJob.editor#perms@IamRole:AnnoJob.viewer#perms
IamRole:AnnoJob.editor#<EMAIL>
IamRole:AnnoJob.editor#<EMAIL>
IamRole:AnnoJob.editor#<EMAIL>
IamRole:AnnoJob.editor#policies@IamPolicy:sys/admin
IamRole:AnnoJob.editor#policies@IamPolicy:sys/publicView
IamRole:AnnoJob.owner#perms@IamRole:AnnoJob.editor#perms
IamRole:AnnoJob.owner#<EMAIL>
IamRole:AnnoJob.owner#<EMAIL>
IamRole:AnnoJob.owner#<EMAIL>
IamRole:AnnoJob.owner#policies@IamPolicy:sys/admin
IamRole:AnnoJob.owner#policies@IamPolicy:sys/publicView

#IamRole:AnnoJob.worker#<EMAIL>
#IamRole:AnnoJob.worker#<EMAIL>
IamRole:AnnoJob.regulator#<EMAIL>
IamRole:AnnoJob.regulator#<EMAIL>
IamRole:AnnoJob.regulator#<EMAIL>
IamRole:AnnoJob.regulator#policies@IamPolicy:sys/admin
IamRole:AnnoJob.regulator#policies@IamPolicy:sys/publicView

IamRole:AnnoOrder.viewer#<EMAIL>
IamRole:AnnoOrder.viewer#<EMAIL>
IamRole:AnnoOrder.viewer#policies@IamPolicy:sys/admin
IamRole:AnnoOrder.viewer#policies@IamPolicy:sys/publicView
IamRole:AnnoOrder.editor#perms@IamRole:AnnoOrder.viewer#perms
IamRole:AnnoOrder.editor#<EMAIL>
IamRole:AnnoOrder.editor#<EMAIL>
IamRole:AnnoOrder.editor#<EMAIL>
IamRole:AnnoOrder.editor#<EMAIL>
IamRole:AnnoOrder.editor#policies@IamPolicy:sys/admin
IamRole:AnnoOrder.editor#policies@IamPolicy:sys/publicView
IamRole:AnnoOrder.owner#perms@IamRole:AnnoOrder.editor#perms
IamRole:AnnoOrder.owner#<EMAIL>
IamRole:AnnoOrder.owner#<EMAIL>
IamRole:AnnoOrder.owner#<EMAIL>
IamRole:AnnoOrder.owner#policies@IamPolicy:sys/admin
IamRole:AnnoOrder.owner#policies@IamPolicy:sys/publicView

IamRole:anno.viewer#perms@IamRole:AnnoLot.viewer#perms
IamRole:anno.viewer#perms@IamRole:AnnoJob.viewer#perms
IamRole:anno.viewer#perms@IamRole:AnnoOrder.viewer#perms
IamRole:anno.viewer#policies@IamPolicy:sys/admin
IamRole:anno.viewer#policies@IamPolicy:sys/publicView

IamRole:anno.editor#perms@IamRole:AnnoLot.editor#perms
IamRole:anno.editor#perms@IamRole:AnnoJob.editor#perms
IamRole:anno.editor#perms@IamRole:AnnoOrder.editor#perms
IamRole:anno.editor#policies@IamPolicy:sys/admin
IamRole:anno.editor#policies@IamPolicy:sys/publicView

IamRole:anno.owner#perms@IamRole:AnnoLot.owner#perms
IamRole:anno.owner#perms@IamRole:AnnoJob.owner#perms
IamRole:anno.owner#perms@IamRole:AnnoOrder.owner#perms
IamRole:anno.owner#policies@IamPolicy:sys/admin
IamRole:anno.owner#policies@IamPolicy:sys/publicView

IamRole:anno.regulator#perms@IamRole:AnnoLot.executor#perms
IamRole:anno.regulator#perms@IamRole:AnnoJob.regulator#perms
IamRole:anno.regulator#policies@IamPolicy:sys/admin
IamRole:anno.regulator#policies@IamPolicy:sys/publicView

IamRole:anno.kam#perms@IamRole:anno.regulator#perms
IamRole:anno.kam#perms@IamRole:AnnoLot.owner#perms
IamRole:anno.kam#perms@IamRole:AnnoJob.owner#perms
IamRole:anno.kam#perms@IamRole:AnnoOrder.owner#perms
IamRole:anno.kam#perms@IamRole:AnnofeedData.owner#perms
IamRole:anno.kam#perms@IamRole:AnnofeedFile.owner#perms
#IamRole:anno.kam#policies@IamPolicy:sys/admin
#IamRole:anno.kam#policies@IamPolicy:sys/publicView

IamRole:anno.pm#perms@IamRole:anno.regulator#perms
IamRole:anno.pm#perms@IamRole:AnnoLot.editor#perms
IamRole:anno.pm#perms@IamRole:AnnoJob.editor#perms
#IamRole:anno.pm#policies@IamPolicy:sys/admin
#IamRole:anno.pm#policies@IamPolicy:sys/publicView

# annofeed roles and permissions
IamRole:AnnofeedData.viewer#<EMAIL>
IamRole:AnnofeedData.viewer#<EMAIL>
IamRole:AnnofeedData.viewer#policies@IamPolicy:sys/admin
IamRole:AnnofeedData.viewer#policies@IamPolicy:sys/publicView
IamRole:AnnofeedData.editor#perms@IamRole:AnnofeedData.viewer#perms
IamRole:AnnofeedData.editor#<EMAIL>
IamRole:AnnofeedData.editor#<EMAIL>
IamRole:AnnofeedData.editor#policies@IamPolicy:sys/admin
IamRole:AnnofeedData.editor#policies@IamPolicy:sys/publicView
IamRole:AnnofeedData.owner#perms@IamRole:AnnofeedData.editor#perms
IamRole:AnnofeedData.owner#<EMAIL>
IamRole:AnnofeedData.owner#<EMAIL>
IamRole:AnnofeedData.owner#<EMAIL>
IamRole:AnnofeedData.owner#policies@IamPolicy:sys/admin
IamRole:AnnofeedData.owner#policies@IamPolicy:sys/publicView

IamRole:AnnofeedFile.viewer#<EMAIL>
IamRole:AnnofeedFile.viewer#<EMAIL>
IamRole:AnnofeedFile.viewer#policies@IamPolicy:sys/admin
IamRole:AnnofeedFile.viewer#policies@IamPolicy:sys/publicView
IamRole:AnnofeedFile.editor#perms@IamRole:AnnofeedFile.viewer#perms
IamRole:AnnofeedFile.editor#<EMAIL>
IamRole:AnnofeedFile.editor#<EMAIL>
IamRole:AnnofeedFile.editor#policies@IamPolicy:sys/admin
IamRole:AnnofeedFile.editor#policies@IamPolicy:sys/publicView
IamRole:AnnofeedFile.owner#perms@IamRole:AnnofeedFile.editor#perms
IamRole:AnnofeedFile.owner#<EMAIL>
IamRole:AnnofeedFile.owner#<EMAIL>
IamRole:AnnofeedFile.owner#<EMAIL>
IamRole:AnnofeedFile.owner#policies@IamPolicy:sys/admin
IamRole:AnnofeedFile.owner#policies@IamPolicy:sys/publicView

IamRole:annofeed.viewer#perms@IamRole:AnnofeedData.viewer#perms
IamRole:annofeed.viewer#perms@IamRole:AnnofeedFile.viewer#perms
IamRole:annofeed.viewer#policies@IamPolicy:sys/admin
IamRole:annofeed.viewer#policies@IamPolicy:sys/publicView

IamRole:annofeed.editor#perms@IamRole:AnnofeedData.editor#perms
IamRole:annofeed.editor#perms@IamRole:AnnofeedFile.editor#perms
IamRole:annofeed.editor#policies@IamPolicy:sys/admin
IamRole:annofeed.editor#policies@IamPolicy:sys/publicView

IamRole:annofeed.owner#perms@IamRole:AnnofeedData.owner#perms
IamRole:annofeed.owner#perms@IamRole:AnnofeedFile.owner#perms
IamRole:annofeed.owner#policies@IamPolicy:sys/admin
IamRole:annofeed.owner#policies@IamPolicy:sys/publicView

# front-end page roles and permissions
IamRole:FePage.viewer#<EMAIL>
IamRole:FePage.viewer#policies@IamPolicy:sys/admin
IamRole:FePage.viewer#policies@IamPolicy:sys/publicView
IamRole:FePage.editor#perms@IamRole:FePage.viewer#perms
IamRole:FePage.editor#policies@IamPolicy:sys/admin
IamRole:FePage.editor#policies@IamPolicy:sys/publicView
IamRole:FePage.owner#perms@IamRole:FePage.editor#perms
IamRole:FePage.owner#<EMAIL>
IamRole:FePage.owner#<EMAIL>
IamRole:FePage.owner#policies@IamPolicy:sys/admin
IamRole:FePage.owner#policies@IamPolicy:sys/publicView

IamRole:fe.viewer#perms@IamRole:FePage.viewer#perms
IamRole:fe.viewer#policies@IamPolicy:sys/admin
IamRole:fe.viewer#policies@IamPolicy:sys/publicView

IamRole:fe.editor#perms@IamRole:FePage.editor#perms
IamRole:fe.editor#policies@IamPolicy:sys/admin
IamRole:fe.editor#policies@IamPolicy:sys/publicView

IamRole:fe.owner#perms@IamRole:FePage.owner#perms
IamRole:fe.owner#policies@IamPolicy:sys/admin
IamRole:fe.owner#policies@IamPolicy:sys/publicView

# make admins can create new things
IamRole:/#policies@IamPolicy:sys/admin
IamUser:/#policies@IamPolicy:sys/admin
IamUser:/#policies@IamPolicy:sys/kam
IamUser:/#policies@IamPolicy:sys/demo
IamGroup:/#policies@IamPolicy:sys/admin
IamGroup:/#policies@IamPolicy:sys/kam
IamGroup:/#policies@IamPolicy:sys/demo
AnnoLot:/#policies@IamPolicy:sys/admin
AnnoOrder:/#policies@IamPolicy:sys/admin
AnnofeedData:/#policies@IamPolicy:sys/admin
AnnofeedFile:/#policies@IamPolicy:sys/admin
FePage:/#policies@IamPolicy:sys/admin

# global implicit policies
IamRole:*#policies@IamPolicy:sys/admin
IamRole:*#policies@IamPolicy:sys/publicView
IamUser:*#policies@IamPolicy:sys/admin
IamUser:*#policies@IamPolicy:sys/inspector
IamUser:*#policies@IamPolicy:sys/kam
IamUser:*#policies@IamPolicy:sys/demo
IamGroup:*#policies@IamPolicy:sys/admin
IamGroup:*#policies@IamPolicy:sys/inspector
IamGroup:*#policies@IamPolicy:sys/kam
IamGroup:*#policies@IamPolicy:sys/pm
IamGroup:*#policies@IamPolicy:sys/demo
AnnoLot:*#policies@IamPolicy:sys/admin
AnnoLot:*#policies@IamPolicy:sys/inspector
AnnoOrder:*#policies@IamPolicy:sys/admin
AnnofeedData:*#policies@IamPolicy:sys/admin
AnnofeedFile:*#policies@IamPolicy:sys/admin
FePage:*#policies@IamPolicy:sys/admin

## team policy template
#IamPolicy:team-xxx.owner#roles@IamRole:owner
#IamPolicy:team-xxx.owner#users@IamGroup:team-xxx.owner#members
#IamGroup:team-xxx.owner#members@IamUser:xxx
#IamGroup:team-xxx#policies@IamPolicy:team-xxx.owner
#IamGroup:team-xxx#policies@IamPolicy:sys/admin
#IamGroup:team-xxx#parents@IamGroup:x
#
#IamPolicy:team-xxx.manager#roles@IamRole:manager
#IamPolicy:team-xxx.manager#users@IamGroup:team-xxx.manager#members
#IamGroup:team-xxx.manager#members@IamUser:yyy
#IamGroup:team-xxx#policies@IamPolicy:team-xxx.manager
#
#IamPolicy:team-xxx.member#roles@IamRole:member
#IamPolicy:team-xxx.member#users@IamGroup:team-xxx.member#members
#IamGroup:team-xxx.member#members@IamUser:xxx
#IamGroup:team-xxx.member#members@IamUser:yyy
#IamGroup:team-xxx#policies@IamPolicy:team-xxx.member
#
## make top-team managers can create new things
#IamRole:IamGroup.xxx#owners@IamGroup:team-xxx
#IamUser:IamGroup.xxx#owners@IamGroup:team-xxx
#
## make team managers can create new things
#AnnoLot:IamGroup.xxx#owners@IamGroup:team-xxx
